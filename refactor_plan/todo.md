# French Sustainability Transcript Analysis - Refactor TODO

## Project Overview
Transform the existing Gemini LangGraph research assistant into a French sustainability transcript analysis tool that identifies and categorizes paradoxes/tensions in organizational sustainability discussions.

## Key Requirements
- **Input**: French transcripts (~300 pages) from group discussions (Groups A-H)
- **Output**: Structured analysis with 12 fields per identified tension/paradox
- **Target Fields**:
  - Concepts de 2nd ordre (Second-order concepts)
  - Items de 1er ordre reformulé (Reformulated first-order items)
  - Items de 1er ordre (intitulé d'origine) (Original first-order items)
  - Détails (Details - text excerpts)
  - Synthèse (Synthesis)
  - Code Entretien (Interview code)
  - Période (Period: 2023/2050)
  - Thème (Theme: Légitimité/Performance)
  - Code spé (Specific code)
  - Constat/Stéréotype + IFa/IFr (Observation/Stereotype + Facilitating/Hindering)
  - Tension de modèle (Model tension)
  - Tension liée au changement (Change-related tension)

## Phase 1: Backend Refactoring

### 1.1 Core Graph Architecture (`backend/src/agent/graph.py`)
- [ ] **Replace web research nodes** with transcript analysis nodes:
  - [ ] `segment_transcript` - Extract tension-containing segments
  - [ ] `analyze_segment` - Analyze individual segments (parallel processing)
  - [ ] `review_analysis` - Optional validation/gap checking
  - [ ] `finalize_output` - Aggregate and format results
- [ ] **Update graph flow**:
  - [ ] START → `segment_transcript`
  - [ ] `segment_transcript` → spawn multiple `analyze_segment` (parallel)
  - [ ] `analyze_segment` → `finalize_output`
  - [ ] `finalize_output` → END
- [ ] **Remove old nodes**: `generate_query`, `web_research`, `reflection`, `finalize_answer`

### 1.2 State Management (`backend/src/agent/state.py`)
- [ ] **Define new state schemas**:
  - [ ] `TranscriptState` - Input transcript and metadata
  - [ ] `SegmentationState` - List of extracted segments
  - [ ] `AnalysisResult` - Single tension analysis result
  - [ ] `OverallState` - Complete workflow state
- [ ] **Remove old states**: `QueryGenerationState`, `WebSearchState`, `ReflectionState`

### 1.3 Prompt Engineering (`backend/src/agent/prompts.py`)
- [ ] **Create French-language prompts**:
  - [ ] `segmentation_instructions` - Find paradox excerpts using contrastive markers
  - [ ] `tension_extraction_instructions` - Extract original/reformulated items
  - [ ] `categorization_instructions` - Assign second-order concepts and codes
  - [ ] `synthesis_instructions` - Generate one-line summaries
  - [ ] `imaginaire_classification_instructions` - C/S and IFa/IFr classification
- [ ] **Include few-shot examples** in French
- [ ] **Remove old prompts**: search-related templates

### 1.4 Schemas and Tools (`backend/src/agent/tools_and_schemas.py`)
- [ ] **Define Pydantic models**:
  - [ ] `SegmentsList` - Segmentation output
  - [ ] `TensionExtraction` - Original/reformulated items
  - [ ] `Categorization` - Concept, code, theme classification
  - [ ] `FullAnalysisResult` - Complete analysis for one segment
- [ ] **Create domain tools**:
  - [ ] Taxonomy mapping dictionary (concept → code)
  - [ ] `assign_code()` function
  - [ ] `detect_period()` function (2023/2050)
  - [ ] `determine_theme()` function (Légitimité/Performance)
- [ ] **Remove old schemas**: `SearchQueryList`, `Reflection`

### 1.5 Utilities (`backend/src/agent/utils.py`)
- [ ] **Add new utilities**:
  - [ ] `clean_transcript()` - Remove timestamps/artifacts
  - [ ] `detect_period()` - Extract temporal markers
  - [ ] `determine_theme()` - Keyword-based theme classification
  - [ ] `format_csv()` - Export results to CSV
- [ ] **Remove old utilities**: citation handling, URL resolution

### 1.6 Configuration (`backend/src/agent/configuration.py`)
- [ ] **Update config options**:
  - [ ] Remove: `number_of_initial_queries`, `max_research_loops`
  - [ ] Add: `max_segments`, `model_temperature`, `use_gpu`
  - [ ] Add: LLM provider selection (Gemini/OpenAI/Local)

## Phase 2: Frontend Refactoring

### 2.1 Main App (`frontend/src/App.tsx`)
- [ ] **Replace Q&A interface** with transcript analysis dashboard
- [ ] **Update data flow** for structured results display
- [ ] **Remove chat-related state** management

### 2.2 Input Interface (`frontend/src/components/InputForm.tsx`)
- [ ] **Replace text input** with:
  - [ ] File upload for transcript files
  - [ ] Large text area for direct paste
  - [ ] Metadata fields (group code, period if known)
- [ ] **Update submission logic** for transcript data

### 2.3 Results Display (New Components)
- [ ] **Create `ResultsTable` component**:
  - [ ] Display 12-column table of tensions
  - [ ] Editable cells for corrections
  - [ ] Save changes functionality
  - [ ] Row selection and highlighting
- [ ] **Create `TranscriptViewer` component** (optional):
  - [ ] Display full transcript with highlighted segments
  - [ ] Link to corresponding table rows

### 2.4 Activity Timeline (Optional)
- [ ] **Repurpose for analysis progress**:
  - [ ] Show segmentation progress
  - [ ] Display analysis steps
  - [ ] Final summary statistics

## Phase 3: Dependencies and Infrastructure

### 3.1 NLP Dependencies
- [ ] **Add spaCy with French model**:
  - [ ] Update requirements.txt: `spacy`, `fr-core-news-lg`
  - [ ] Update Dockerfile: `RUN python -m spacy download fr_core_news_lg`
- [ ] **Optional: Additional models** (OpenAI, local French models)

### 3.2 Domain Data
- [ ] **Create taxonomy files**:
  - [ ] `backend/src/agent/taxonomy.json` - Concept mappings
  - [ ] `backend/src/agent/taxonomy.py` - Lookup functions
- [ ] **Example data**:
  - [ ] Sample transcript for testing
  - [ ] Expected output CSV

### 3.3 Docker Configuration
- [ ] **Update Dockerfile**:
  - [ ] Add spaCy installation
  - [ ] Remove Google Search dependencies
  - [ ] Update environment variables
- [ ] **Update docker-compose.yml**:
  - [ ] Maintain Postgres and Redis services
  - [ ] Update volume mounts if needed

## Phase 4: Testing and Validation

### 4.1 Unit Tests
- [ ] **Test individual nodes**:
  - [ ] Segmentation logic
  - [ ] Tension extraction
  - [ ] Classification accuracy
- [ ] **Test utility functions**:
  - [ ] Period detection
  - [ ] Theme classification
  - [ ] Code assignment

### 4.2 Integration Tests
- [ ] **End-to-end workflow** with sample transcript
- [ ] **Frontend-backend integration**
- [ ] **Docker deployment testing**

### 4.3 Expert Validation
- [ ] **Compare outputs** with expert annotations
- [ ] **Iterative prompt refinement**
- [ ] **Feedback incorporation mechanism**

## Phase 5: Documentation and Deployment

### 5.1 Documentation
- [ ] **Update README.md**:
  - [ ] New usage instructions
  - [ ] Configuration options
  - [ ] Model selection guide
- [ ] **API documentation** for new endpoints
- [ ] **User guide** for researchers

### 5.2 Production Readiness
- [ ] **Performance optimization**:
  - [ ] Concurrent processing limits
  - [ ] Memory usage monitoring
- [ ] **Error handling** and logging
- [ ] **Data persistence** and backup

## Key Technical Decisions to Discuss

1. **LLM Provider**: Continue with Gemini or switch to OpenAI/local model?
2. **Segmentation Strategy**: Rule-based (spaCy) vs LLM-based vs hybrid?
3. **Processing Approach**: Sequential vs parallel segment analysis?
4. **Validation Loop**: Include iterative refinement or single-pass?
5. **Frontend Complexity**: Simple table vs rich interactive interface?
6. **Data Storage**: LangGraph persistence vs custom database?

## Estimated Timeline
- **Phase 1 (Backend)**: 2-3 weeks
- **Phase 2 (Frontend)**: 1-2 weeks  
- **Phase 3 (Infrastructure)**: 1 week
- **Phase 4 (Testing)**: 1-2 weeks
- **Phase 5 (Documentation)**: 1 week

**Total**: 6-9 weeks depending on complexity choices and validation requirements.
